import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import prisma from '@/lib/db';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

interface RequestBody {
  message: string;
  selectedDatasets: string[];
  conversationHistory?: Array<{ role: string; content: string }>;
  model?: string;
  images?: Array<{ name: string; data: string; mimeType: string }>;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body: RequestBody = await req.json();
    const {
      message,
      selectedDatasets = [],
      conversationHistory = [],
      model = 'gemini-1.5-flash',
      images = []
    } = body;

    // Validate input
    if (!message || typeof message !== 'string' || message.trim() === '') {
      return NextResponse.json({
        success: false,
        error: 'Message is required'
      }, { status: 400 });
    }

    if (!selectedDatasets || (selectedDatasets.length === 0 && images.length === 0)) {
      return NextResponse.json({
        success: false,
        error: 'Please select at least one dataset or upload an image to analyze'
      }, { status: 400 });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Fetch the selected datasets with their actual data (if any)
    let datasets: any[] = [];
    if (selectedDatasets.length > 0) {
      datasets = await prisma.dataSet.findMany({
        where: {
          id: { in: selectedDatasets },
          userId: user.id
        },
        select: {
          id: true,
          name: true,
          description: true,
          data: true,
          headers: true,
          fileType: true
        }
      });

      if (datasets.length === 0 && images.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'No accessible datasets found'
        }, { status: 404 });
      }
    }

    // Prepare dataset information for AI with more context
    const datasetInfo = datasets.map(dataset => {
      const data = Array.isArray(dataset.data) ? dataset.data : [];
      const sampleData = data.slice(0, 10); // Show first 10 rows as sample for better context

      return {
        name: dataset.name,
        description: dataset.description || 'No description',
        headers: dataset.headers || [],
        totalRows: data.length,
        sampleData: sampleData,
        fileType: dataset.fileType,
        fullData: data // Include full data for accurate analysis
      };
    });

    // Create the AI prompt with better instructions for references
    let systemPrompt = `You are an advanced AI data analyst assistant. Your job is to analyze datasets and/or images and answer questions in a natural, conversational way.

${datasets.length > 0 ? `
AVAILABLE DATASETS:
${datasetInfo.map(ds => `
Dataset: "${ds.name}"
Description: ${ds.description}
Columns: ${ds.headers.join(', ')}
Total Rows: ${ds.totalRows}
Sample Data (first 10 rows):
${ds.sampleData.map((row: any, i: number) => `Row ${i + 1}: ${JSON.stringify(row)}`).join('\n')}

FULL DATASET FOR ANALYSIS:
${JSON.stringify(ds.fullData)}
`).join('\n---\n')}
` : ''}

${images.length > 0 ? `
UPLOADED IMAGES: ${images.length} image(s) provided for analysis
` : ''}

INSTRUCTIONS:
1. Answer questions directly and accurately by analyzing ALL available data
2. ALWAYS provide specific references and sources to support your answers
3. When counting or analyzing datasets, examine ALL rows, not just samples
4. For image analysis, describe what you see and extract any text/data using OCR
5. Be specific with numbers and provide clear explanations
6. Show your reasoning and calculations when needed
7. Use natural language, avoid technical jargon
8. Provide insights and context about the findings

REFERENCE FORMAT - ALWAYS include sources in your response:
- For dataset analysis: "Based on analysis of [Dataset Name] (X total rows), I found..."
- For specific examples: "For example, in [Dataset Name], Row X shows..."
- For calculations: "Calculating from [Dataset Name]: [show calculation]..."
- For image analysis: "From the uploaded image '[Image Name]', I can see..."
- For OCR text: "The text extracted from '[Image Name]' shows..."

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

USER QUESTION: ${message}

Please analyze thoroughly and provide a helpful answer with proper source references.`;

    // Retry logic with fallback models
    const retryWithFallback = async (primaryModel: string, maxRetries: number = 3): Promise<string> => {
      const fallbackModels = [
        primaryModel,
        'gemini-1.5-flash-002',
        'gemini-1.5-flash',
        'gemini-1.0-pro'
      ];

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        for (const modelToTry of fallbackModels) {
          try {
            console.log(`Attempt ${attempt + 1}: Trying model ${modelToTry}`);

            const geminiModel = genAI.getGenerativeModel({
              model: modelToTry,
              generationConfig: {
                temperature: 0.1,
                topP: 0.8,
                topK: 40,
                maxOutputTokens: 2048,
              }
            });

            // Prepare content for Gemini (text + images)
            const parts: any[] = [{ text: systemPrompt }];

            // Add images if provided and model supports them
            const supportsImages = modelToTry.includes('1.5');
            if (images.length > 0 && supportsImages) {
              images.forEach(img => {
                parts.push({
                  inlineData: {
                    mimeType: img.mimeType,
                    data: img.data
                  }
                });
              });
            } else if (images.length > 0 && !supportsImages) {
              // Add image descriptions to text for non-vision models
              parts[0].text += `\n\nNote: ${images.length} image(s) were uploaded but this model doesn't support image analysis. Please describe what you'd like to know about the images.`;
            }

            const result = await geminiModel.generateContent(parts);
            const response = result.response.text();

            console.log(`Successfully generated response with model: ${modelToTry}`);
            return response;

          } catch (error: any) {
            console.log(`Model ${modelToTry} failed on attempt ${attempt + 1}:`, error.message);

            // If it's a 503 (overloaded) error, wait before retrying
            if (error.status === 503) {
              const waitTime = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s
              console.log(`Waiting ${waitTime}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }

            // Continue to next model or retry
            continue;
          }
        }
      }

      throw new Error('All models failed after multiple retries. Please try again later.');
    };

    console.log('Sending request to Gemini:', {
      model,
      datasets: datasets.map(d => ({ name: d.name, rows: Array.isArray(d.data) ? d.data.length : 0 })),
      images: images.length
    });

    const response = await retryWithFallback(model);

    // Extract sources from the response (simple pattern matching)
    const sources: Array<{ dataset: string; reference: string; type: 'data' | 'calculation' | 'example' }> = [];

    // Look for dataset references in the response
    datasets.forEach(dataset => {
      const datasetName = dataset.name;
      if (response.includes(datasetName)) {
        // Check for different types of references
        if (response.includes(`analysis of ${datasetName}`) || response.includes(`Based on ${datasetName}`)) {
          sources.push({
            dataset: datasetName,
            reference: `Analysis performed on ${datasetName} dataset with ${Array.isArray(dataset.data) ? dataset.data.length : 0} rows`,
            type: 'data'
          });
        }
        if (response.includes(`Row`) && response.includes(datasetName)) {
          sources.push({
            dataset: datasetName,
            reference: `Specific examples found in ${datasetName} dataset`,
            type: 'example'
          });
        }
        if (response.includes(`Calculating`) || response.includes(`calculation`)) {
          sources.push({
            dataset: datasetName,
            reference: `Calculations performed using ${datasetName} data`,
            type: 'calculation'
          });
        }
      }
    });

    // Add image sources if images were analyzed
    if (images.length > 0) {
      images.forEach(img => {
        sources.push({
          dataset: img.name,
          reference: `Image analysis and OCR performed on uploaded image`,
          type: 'data'
        });
      });
    }

    // Log for debugging
    console.log('AI Response generated successfully');

    return NextResponse.json({
      success: true,
      content: response,
      model: model,
      datasetsAnalyzed: datasets.map(d => ({
        name: d.name,
        totalRows: Array.isArray(d.data) ? d.data.length : 0
      })),
      sources: sources,
      imagesAnalyzed: images.length
    });

  } catch (error: any) {
    console.error('Error in simple AI chat:', error);
    return NextResponse.json({ 
      success: false, 
      error: `Internal server error: ${error.message}` 
    }, { status: 500 });
  }
}
