'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  User,
  Sparkles,
  Gem,
  Quote,
  Database,
  BarChart3,
  Copy,
  Check,
  Download,
  Save
} from 'lucide-react'
import { cn } from '@/lib/utils'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { toast } from 'sonner'
import { useState } from 'react'
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Scatter<PERSON>hart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts'

interface Message {
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  datasetsAnalyzed?: Array<{ name: string; totalRows: number }>
  sources?: Array<{ dataset: string; reference: string; type: 'data' | 'calculation' | 'example' }>
  images?: Array<{ name: string; url: string }>
  model?: string
  chart?: {
    type: 'bar' | 'line' | 'pie' | 'scatter'
    data: any
    title?: string
  }
  table?: {
    headers: string[]
    rows: any[][]
    title?: string
  }
}

interface MessageBubbleProps {
  message: Message
  modelName?: string
}

export function MessageBubble({ message, modelName }: MessageBubbleProps) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      setCopied(true)
      toast.success('Message copied to clipboard')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('Failed to copy message')
    }
  }

  const handleSaveChart = () => {
    if (message.chart) {
      toast.success('Chart saved! (Feature to be implemented)')
    }
  }

  const handleSaveTable = () => {
    if (message.table) {
      toast.success('Table saved! (Feature to be implemented)')
    }
  }

  const renderChart = () => {
    if (!message.chart) return null

    const { type, data, title } = message.chart

    // Transform Chart.js format to Recharts format
    const transformedData = data.labels?.map((label: string, index: number) => {
      const item: any = { name: label }
      data.datasets?.forEach((dataset: any, datasetIndex: number) => {
        item[dataset.label || `Dataset ${datasetIndex + 1}`] = dataset.data[index]
      })
      return item
    }) || []

    const colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#06B6D4']

    const renderChartComponent = () => {
      switch (type) {
        case 'bar':
          return (
            <BarChart data={transformedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {data.datasets?.map((dataset: any, index: number) => (
                <Bar
                  key={index}
                  dataKey={dataset.label || `Dataset ${index + 1}`}
                  fill={colors[index % colors.length]}
                />
              ))}
            </BarChart>
          )
        case 'line':
          return (
            <LineChart data={transformedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {data.datasets?.map((dataset: any, index: number) => (
                <Line
                  key={index}
                  type="monotone"
                  dataKey={dataset.label || `Dataset ${index + 1}`}
                  stroke={colors[index % colors.length]}
                  strokeWidth={2}
                />
              ))}
            </LineChart>
          )
        case 'pie':
          const pieData = data.datasets?.[0]?.data?.map((value: number, index: number) => ({
            name: data.labels?.[index] || `Item ${index + 1}`,
            value: value
          })) || []

          return (
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {pieData.map((entry: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          )
        case 'scatter':
          return (
            <ScatterChart data={transformedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {data.datasets?.map((dataset: any, index: number) => (
                <Scatter
                  key={index}
                  dataKey={dataset.label || `Dataset ${index + 1}`}
                  fill={colors[index % colors.length]}
                />
              ))}
            </ScatterChart>
          )
        default:
          return (
            <BarChart data={transformedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {data.datasets?.map((dataset: any, index: number) => (
                <Bar
                  key={index}
                  dataKey={dataset.label || `Dataset ${index + 1}`}
                  fill={colors[index % colors.length]}
                />
              ))}
            </BarChart>
          )
      }
    }

    return (
      <Card className="mt-3">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              {title || 'Generated Chart'}
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSaveChart}
              className="h-7 px-2"
            >
              <Save className="h-3 w-3 mr-1" />
              Save
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64 w-full">
            <ResponsiveContainer width="100%" height="100%">
              {renderChartComponent()}
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderTable = () => {
    if (!message.table) return null

    const { headers, rows, title } = message.table

    return (
      <Card className="mt-3">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Database className="h-4 w-4" />
              {title || 'Generated Table'}
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSaveTable}
              className="h-7 px-2"
            >
              <Save className="h-3 w-3 mr-1" />
              Save
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="max-h-64 overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  {headers.map((header, i) => (
                    <TableHead key={i}>{header}</TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {rows.map((row, i) => (
                  <TableRow key={i}>
                    {row.map((cell, j) => (
                      <TableCell key={j}>{cell}</TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn(
      "flex gap-3 group",
      message.role === 'user' ? "justify-end" : "justify-start"
    )}>
      {/* Avatar */}
      {message.role === 'assistant' && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center shadow-lg">
          <Sparkles className="h-4 w-4 text-primary-foreground" />
        </div>
      )}

      {/* Message Content */}
      <div className={cn(
        "max-w-[85%] rounded-2xl shadow-sm transition-all duration-200 hover:shadow-md",
        message.role === 'user'
          ? "bg-primary text-primary-foreground"
          : "bg-card border"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 pb-2">
          <div className="flex items-center gap-2">
            <div className={cn(
              "p-1.5 rounded-full",
              message.role === 'user'
                ? "bg-primary-foreground/20"
                : "bg-muted"
            )}>
              {message.role === 'user' ? (
                <User className="h-3 w-3" />
              ) : (
                <Sparkles className="h-3 w-3 text-primary" />
              )}
            </div>
            <span className={cn(
              "text-sm font-medium",
              message.role === 'user' ? "text-primary-foreground" : "text-foreground"
            )}>
              {message.role === 'user' ? 'You' : 'AI Assistant'}
            </span>
            {message.model && message.role === 'assistant' && modelName && (
              <Badge variant="outline" className="text-xs">
                <Gem className="h-3 w-3 mr-1" />
                {modelName}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <span className={cn(
              "text-xs",
              message.role === 'user' ? "text-primary-foreground/70" : "text-muted-foreground"
            )}>
              {message.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
            {message.role === 'assistant' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                {copied ? (
                  <Check className="h-3 w-3 text-green-500" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Images */}
        {message.images && message.images.length > 0 && (
          <div className="px-3 pb-2">
            <div className="flex flex-wrap gap-2">
              {message.images.map((img, i) => (
                <div key={i} className="relative group/img">
                  <img
                    src={img.url}
                    alt={img.name}
                    className="w-16 h-16 object-cover rounded-lg border shadow-sm hover:shadow-md transition-shadow"
                  />
                  <div className="absolute inset-0 bg-black/0 hover:bg-black/10 rounded-lg transition-colors" />
                  <div className="absolute bottom-0 left-0 right-0 bg-black/60 rounded-b-lg p-1">
                    <span className="text-white text-xs font-medium truncate block">
                      {img.name}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="px-3 pb-3">
          <div className={cn(
            "prose prose-sm max-w-none",
            message.role === 'user'
              ? "prose-invert"
              : "prose-gray dark:prose-invert"
          )}>
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // Custom styling for markdown elements
                h1: ({ children }) => (
                  <h1 className="text-lg font-bold mb-2 mt-0">{children}</h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-base font-semibold mb-2 mt-3">{children}</h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-sm font-medium mb-1 mt-2">{children}</h3>
                ),
                p: ({ children }) => (
                  <p className="mb-2 last:mb-0 leading-relaxed">{children}</p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
                ),
                code: ({ children, className }) => {
                  const isInline = !className
                  return isInline ? (
                    <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
                      {children}
                    </code>
                  ) : (
                    <code className="block bg-muted p-3 rounded-lg text-sm font-mono overflow-x-auto">
                      {children}
                    </code>
                  )
                },
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-primary pl-4 italic my-2">
                    {children}
                  </blockquote>
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto">
                    <table className="min-w-full border-collapse border text-sm">
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="border px-2 py-1 bg-muted font-medium text-left">
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="border px-2 py-1">
                    {children}
                  </td>
                )
              }}
            >
              {message.content}
            </ReactMarkdown>
          </div>

          {/* Render Chart */}
          {renderChart()}

          {/* Render Table */}
          {renderTable()}
        </div>

        {/* Sources */}
        {message.sources && message.sources.length > 0 && (
          <div className="px-3 pb-3 pt-0">
            <div className="border-t pt-3">
              <div className="flex items-center gap-2 mb-2">
                <Quote className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium text-foreground">
                  Sources
                </span>
              </div>
              <div className="space-y-2">
                {message.sources.map((source, i) => (
                  <div key={i} className="flex items-start gap-2 p-2 bg-muted rounded-lg">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-primary">
                        {i + 1}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          <Database className="h-3 w-3 mr-1" />
                          {source.dataset}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {source.type}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {source.reference}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Datasets Analyzed */}
        {message.datasetsAnalyzed && message.datasetsAnalyzed.length > 0 && (
          <div className="px-3 pb-3">
            <div className="border-t pt-3">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium text-foreground">
                  Analyzed Datasets
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                {message.datasetsAnalyzed.map((ds, i) => (
                  <Badge key={i} variant="outline" className="text-xs">
                    <Database className="h-3 w-3 mr-1 text-primary" />
                    {ds.name} ({ds.totalRows.toLocaleString()} rows)
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* User Avatar */}
      {message.role === 'user' && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-muted flex items-center justify-center shadow-lg">
          <User className="h-4 w-4 text-muted-foreground" />
        </div>
      )}
    </div>
  )
}
