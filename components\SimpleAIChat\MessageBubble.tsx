'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  User, 
  <PERSON>rkles, 
  Gem, 
  Quote, 
  Database, 
  BarChart3,
  Copy,
  Check
} from 'lucide-react'
import { cn } from '@/lib/utils'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { toast } from 'sonner'
import { useState } from 'react'

interface Message {
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  datasetsAnalyzed?: Array<{ name: string; totalRows: number }>
  sources?: Array<{ dataset: string; reference: string; type: 'data' | 'calculation' | 'example' }>
  images?: Array<{ name: string; url: string }>
  model?: string
}

interface MessageBubbleProps {
  message: Message
  modelName?: string
}

export function MessageBubble({ message, modelName }: MessageBubbleProps) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      setCopied(true)
      toast.success('Message copied to clipboard')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('Failed to copy message')
    }
  }

  return (
    <div className={cn(
      "flex gap-3 group",
      message.role === 'user' ? "justify-end" : "justify-start"
    )}>
      {/* Avatar */}
      {message.role === 'assistant' && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
          <Sparkles className="h-4 w-4 text-white" />
        </div>
      )}

      {/* Message Content */}
      <div className={cn(
        "max-w-[85%] rounded-2xl shadow-sm transition-all duration-200 hover:shadow-md",
        message.role === 'user'
          ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white"
          : "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 pb-2">
          <div className="flex items-center gap-2">
            <div className={cn(
              "p-1.5 rounded-full",
              message.role === 'user'
                ? "bg-white/20"
                : "bg-blue-50 dark:bg-blue-950"
            )}>
              {message.role === 'user' ? (
                <User className="h-3 w-3" />
              ) : (
                <Sparkles className="h-3 w-3 text-blue-600" />
              )}
            </div>
            <span className={cn(
              "text-sm font-medium",
              message.role === 'user' ? "text-white" : "text-gray-900 dark:text-gray-100"
            )}>
              {message.role === 'user' ? 'You' : 'AI Assistant'}
            </span>
            {message.model && message.role === 'assistant' && modelName && (
              <Badge variant="outline" className="text-xs bg-white/10 border-white/20">
                <Gem className="h-3 w-3 mr-1" />
                {modelName}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <span className={cn(
              "text-xs",
              message.role === 'user' ? "text-white/70" : "text-gray-500"
            )}>
              {message.timestamp.toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </span>
            {message.role === 'assistant' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                className={cn(
                  "h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity",
                  message.role === 'user' ? "hover:bg-white/20" : "hover:bg-gray-100 dark:hover:bg-gray-800"
                )}
              >
                {copied ? (
                  <Check className="h-3 w-3 text-green-500" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Images */}
        {message.images && message.images.length > 0 && (
          <div className="px-3 pb-2">
            <div className="flex flex-wrap gap-2">
              {message.images.map((img, i) => (
                <div key={i} className="relative group/img">
                  <img
                    src={img.url}
                    alt={img.name}
                    className="w-16 h-16 object-cover rounded-lg border shadow-sm hover:shadow-md transition-shadow"
                  />
                  <div className="absolute inset-0 bg-black/0 hover:bg-black/10 rounded-lg transition-colors" />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent rounded-b-lg p-1">
                    <span className="text-white text-xs font-medium truncate block">
                      {img.name}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="px-3 pb-3">
          <div className={cn(
            "prose prose-sm max-w-none",
            message.role === 'user' 
              ? "prose-invert" 
              : "prose-gray dark:prose-invert"
          )}>
            <ReactMarkdown 
              remarkPlugins={[remarkGfm]}
              components={{
                // Custom styling for markdown elements
                h1: ({ children }) => (
                  <h1 className="text-lg font-bold mb-2 mt-0">{children}</h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-base font-semibold mb-2 mt-3">{children}</h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-sm font-medium mb-1 mt-2">{children}</h3>
                ),
                p: ({ children }) => (
                  <p className="mb-2 last:mb-0 leading-relaxed">{children}</p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
                ),
                code: ({ children, className }) => {
                  const isInline = !className
                  return isInline ? (
                    <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono">
                      {children}
                    </code>
                  ) : (
                    <code className="block bg-gray-100 dark:bg-gray-800 p-3 rounded-lg text-sm font-mono overflow-x-auto">
                      {children}
                    </code>
                  )
                },
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-blue-500 pl-4 italic my-2">
                    {children}
                  </blockquote>
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto">
                    <table className="min-w-full border-collapse border border-gray-300 dark:border-gray-600 text-sm">
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="border border-gray-300 dark:border-gray-600 px-2 py-1 bg-gray-50 dark:bg-gray-800 font-medium text-left">
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="border border-gray-300 dark:border-gray-600 px-2 py-1">
                    {children}
                  </td>
                )
              }}
            >
              {message.content}
            </ReactMarkdown>
          </div>
        </div>

        {/* Sources */}
        {message.sources && message.sources.length > 0 && (
          <div className="px-3 pb-3 pt-0">
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div className="flex items-center gap-2 mb-2">
                <Quote className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Sources
                </span>
              </div>
              <div className="space-y-2">
                {message.sources.map((source, i) => (
                  <div key={i} className="flex items-start gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                        {i + 1}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          <Database className="h-3 w-3 mr-1" />
                          {source.dataset}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {source.type}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
                        {source.reference}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Datasets Analyzed */}
        {message.datasetsAnalyzed && message.datasetsAnalyzed.length > 0 && (
          <div className="px-3 pb-3">
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Analyzed Datasets
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                {message.datasetsAnalyzed.map((ds, i) => (
                  <Badge key={i} variant="outline" className="text-xs bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
                    <Database className="h-3 w-3 mr-1 text-green-600" />
                    {ds.name} ({ds.totalRows.toLocaleString()} rows)
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* User Avatar */}
      {message.role === 'user' && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center shadow-lg">
          <User className="h-4 w-4 text-white" />
        </div>
      )}
    </div>
  )
}
