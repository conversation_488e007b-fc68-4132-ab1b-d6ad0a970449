'use client'

import React, { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Send, 
  ImageIcon, 
  Loader2, 
  X,
  <PERSON>c<PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface UploadedImage {
  name: string
  url: string
  file: File
}

interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  onImageUpload: (files: File[]) => void
  uploadedImages: UploadedImage[]
  onRemoveImage: (index: number) => void
  isLoading: boolean
  disabled?: boolean
  placeholder?: string
  supportsImages?: boolean
}

export function ChatInput({
  value,
  onChange,
  onSend,
  onImageUpload,
  uploadedImages,
  onRemoveImage,
  isLoading,
  disabled = false,
  placeholder = "Type your message...",
  supportsImages = true
}: ChatInputProps) {
  const [isDragActive, setIsDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      const newHeight = Math.min(textarea.scrollHeight, 120) // Max 120px
      textarea.style.height = `${newHeight}px`
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value)
    adjustTextareaHeight()
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (!isLoading && (value.trim() || uploadedImages.length > 0)) {
        onSend()
      }
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'))
    
    if (imageFiles.length !== files.length) {
      toast.error('Please upload only image files')
    }

    if (imageFiles.length > 0) {
      onImageUpload(imageFiles)
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)

    const files = Array.from(e.dataTransfer.files).filter(f => f.type.startsWith('image/'))
    if (files.length > 0) {
      onImageUpload(files)
    }
  }

  const canSend = !isLoading && !disabled && (value.trim() || uploadedImages.length > 0)

  return (
    <div className="space-y-3">
      {/* Uploaded Images Preview */}
      {uploadedImages.length > 0 && (
        <div className="p-3 rounded-lg border bg-muted/50">
          <div className="flex items-center gap-2 mb-3">
            <div className="p-1.5 bg-primary/10 rounded-lg">
              <ImageIcon className="h-4 w-4 text-primary" />
            </div>
            <span className="text-sm font-medium">Uploaded Images</span>
            <Badge variant="secondary" className="text-xs">
              {uploadedImages.length}
            </Badge>
          </div>
          <div className="flex flex-wrap gap-3">
            {uploadedImages.map((img, i) => (
              <div key={i} className="relative group">
                <div className="relative overflow-hidden rounded-lg border-2 border-white shadow-lg">
                  <img
                    src={img.url}
                    alt={img.name}
                    className="w-16 h-16 object-cover transition-transform group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>
                <button
                  onClick={() => onRemoveImage(i)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-destructive hover:bg-destructive/90 text-destructive-foreground rounded-full flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg"
                >
                  <X className="h-3 w-3" />
                </button>
                <div className="absolute bottom-0 left-0 right-0 bg-black/60 rounded-b-lg p-1">
                  <span className="text-white text-xs font-medium truncate block">
                    {img.name}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div
        className={cn(
          "relative rounded-2xl border-2 transition-all duration-200 bg-card shadow-lg",
          isDragActive
            ? "border-primary ring-4 ring-primary/20 bg-primary/5"
            : "border-border hover:border-primary/50",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Drag overlay */}
        {isDragActive && (
          <div className="absolute inset-0 bg-primary/10 rounded-2xl flex items-center justify-center z-10">
            <div className="text-center">
              <ImageIcon className="h-8 w-8 text-primary mx-auto mb-2" />
              <p className="text-sm font-medium text-primary">Drop images here</p>
            </div>
          </div>
        )}

        <div className="flex items-end gap-2 p-3">
          {/* Attachment Button */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={!supportsImages || isLoading || disabled}
            className="flex-shrink-0 h-10 w-10 p-0"
          >
            <Paperclip className="h-4 w-4 text-muted-foreground" />
          </Button>

          {/* Text Input */}
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={uploadedImages.length > 0
                ? "Ask about your data or uploaded images..."
                : placeholder
              }
              disabled={isLoading || disabled}
              className="min-h-[40px] max-h-[120px] resize-none border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-sm leading-relaxed"
              style={{ height: '40px' }}
            />
          </div>

          {/* Image Upload Button */}
          {supportsImages && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading || disabled}
              className="flex-shrink-0 h-10 w-10 p-0"
            >
              <ImageIcon className="h-4 w-4 text-primary" />
            </Button>
          )}

          {/* Send Button */}
          <Button
            onClick={onSend}
            disabled={!canSend}
            className="flex-shrink-0 h-10 w-10 p-0 rounded-xl"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageUpload}
          className="hidden"
        />
      </div>

      {/* Model warning */}
      {uploadedImages.length > 0 && !supportsImages && (
        <div className="p-3 rounded-lg border bg-muted/50">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-yellow-500" />
            <span className="text-sm text-muted-foreground">
              Current model doesn't support images. Switch to Gemini 1.5 Pro or Flash for image analysis.
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
