"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Trash2,
  Sparkles,
  Gem,
  Settings,
  ChevronDown,
  ChevronUp,
  Zap,
  Brain
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Import new components
import { MessageBubble } from './MessageBubble';
import { ChatInput } from './ChatInput';
import { DatasetSidebar } from './DatasetSidebar';
import { TypingIndicator, ShimmerText } from './ShimmerText';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  datasetsAnalyzed?: Array<{ name: string; totalRows: number }>;
  sources?: Array<{ dataset: string; reference: string; type: 'data' | 'calculation' | 'example' }>;
  images?: Array<{ name: string; url: string }>;
  model?: string;
}

interface UploadedImage {
  name: string;
  url: string;
  file: File;
}

interface GeminiModel {
  id: string;
  name: string;
  description: string;
  supportsImages: boolean;
}

interface Dataset {
  id: string;
  name: string;
  description?: string;
  data: any[];
  headers: string[];
}

interface SimpleAIChatInterfaceProps {
  datasets: Dataset[];
  selectedDatasets: string[];
  onDatasetSelectionChange: (datasetIds: string[]) => void;
}

const GEMINI_MODELS: GeminiModel[] = [
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    description: 'Most capable model with excellent reasoning and image analysis',
    supportsImages: true
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    description: 'Fast and efficient with good performance and image support',
    supportsImages: true
  },
  {
    id: 'gemini-1.0-pro',
    name: 'Gemini 1.0 Pro',
    description: 'Reliable and stable for general tasks',
    supportsImages: false
  }
];

const SimpleAIChatInterface: React.FC<SimpleAIChatInterfaceProps> = ({
  datasets,
  selectedDatasets,
  onDatasetSelectionChange
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('gemini-1.5-flash');
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Handle image upload
  const handleImageUpload = (files: File[]) => {
    const selectedModelData = GEMINI_MODELS.find(m => m.id === selectedModel);
    if (!selectedModelData?.supportsImages) {
      toast.error('Selected model does not support image analysis. Please choose Gemini 1.5 Pro or Flash.');
      return;
    }

    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const url = e.target?.result as string;
          setUploadedImages(prev => [...prev, { name: file.name, url, file }]);
          toast.success(`Image "${file.name}" uploaded successfully`);
        };
        reader.readAsDataURL(file);
      } else {
        toast.error('Please upload only image files');
      }
    });
  };

  // Remove uploaded image
  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if ((!input.trim() && uploadedImages.length === 0) || isLoading) return;

    if (selectedDatasets.length === 0 && uploadedImages.length === 0) {
      toast.error('Please select at least one dataset or upload an image to analyze');
      return;
    }

    const userMessage: Message = {
      role: 'user',
      content: input.trim() || 'Analyze the uploaded image(s)',
      timestamp: new Date(),
      images: uploadedImages.map(img => ({ name: img.name, url: img.url })),
      model: selectedModel
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = input.trim();
    const currentImages = [...uploadedImages];
    setInput('');
    setUploadedImages([]);
    setIsLoading(true);

    try {
      // Prepare images for API call
      const imageData = await Promise.all(
        currentImages.map(async (img) => {
          return {
            name: img.name,
            data: img.url.split(',')[1], // Remove data:image/...;base64, prefix
            mimeType: img.file.type
          };
        })
      );

      const response = await fetch('/api/simple-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput || 'Analyze the uploaded image(s)',
          selectedDatasets,
          conversationHistory: messages.slice(-6), // Last 6 messages for context
          model: selectedModel,
          images: imageData
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to get AI response');
      }

      const assistantMessage: Message = {
        role: 'assistant',
        content: data.content,
        timestamp: new Date(),
        datasetsAnalyzed: data.datasetsAnalyzed,
        sources: data.sources || [],
        model: selectedModel
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error: any) {
      console.error('Error sending message:', error);
      toast.error(error.message || 'Failed to send message');
      
      const errorMessage: Message = {
        role: 'assistant',
        content: `Sorry, I encountered an error while analyzing your data: ${error.message}. ${error.message.includes('overloaded') ? 'The AI service is currently busy. Please try again in a moment.' : 'Please try again.'}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearChat = () => {
    setMessages([]);
    setUploadedImages([]);
    toast.success('Chat cleared');
  };

  const toggleDataset = (datasetId: string) => {
    const newSelection = selectedDatasets.includes(datasetId)
      ? selectedDatasets.filter(id => id !== datasetId)
      : [...selectedDatasets, datasetId];
    onDatasetSelectionChange(newSelection);
  };

  const selectedDatasetsInfo = datasets.filter(d => selectedDatasets.includes(d.id));
  const totalRows = selectedDatasetsInfo.reduce((sum, d) => sum + (d.data?.length || 0), 0);

  // Drag and drop state
  const [isDragActive, setIsDragActive] = useState(false);

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  };
  const handleDragLeave = (e: React.DragEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
  };
  const handleDrop = (e: React.DragEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    const files = Array.from(e.dataTransfer.files).filter(f => f.type.startsWith('image/'));
    if (files.length > 0) {
      // Simulate file input upload
      files.forEach(file => {
        const reader = new FileReader();
        reader.onload = (ev) => {
          const url = ev.target?.result as string;
          setUploadedImages(prev => [...prev, { name: file.name, url, file }]);
          toast.success(`Image "${file.name}" uploaded successfully`);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-950 dark:via-gray-900 dark:to-blue-950">
      {/* Left Sidebar - Datasets */}
      <DatasetSidebar
        datasets={datasets}
        selectedDatasets={selectedDatasets}
        onDatasetToggle={toggleDataset}
      />
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col min-w-0 h-full">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  AI Data Assistant
                </h1>
                <p className="text-sm text-muted-foreground">
                  Analyze your data with AI-powered insights
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Settings
                {isSettingsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
              {messages.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearChat}
                  className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear
                </Button>
              )}
            </div>
          </div>

          {/* Settings Panel */}
          {isSettingsOpen && (
            <div className="mt-4 p-4 rounded-xl border bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Gem className="h-4 w-4 text-amber-500" />
                    AI Model
                  </label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {GEMINI_MODELS.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex items-center gap-2">
                            <Gem className="h-4 w-4 text-amber-500" />
                            <div>
                              <div className="font-medium">{model.name}</div>
                              <div className="text-xs text-muted-foreground">{model.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Zap className="h-4 w-4 text-blue-500" />
                    Quick Actions
                  </label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setInput("Analyze the key trends in my data")}
                      className="flex-1"
                    >
                      <Brain className="h-4 w-4 mr-1" />
                      Analyze
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setInput("Create a summary report")}
                      className="flex-1"
                    >
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Report
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        {/* Messages Area */}
        <div className="flex-1 flex flex-col min-h-0 bg-white dark:bg-gray-950">
          <ScrollArea className="flex-1 p-6" ref={scrollAreaRef}>
            <div className="max-w-4xl mx-auto space-y-6">
              {messages.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-3xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-2xl">
                    <Sparkles className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Ready to analyze your data
                  </h3>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    Ask questions about your datasets, request insights, or get help with data analysis.
                  </p>
                  {selectedDatasets.length === 0 ? (
                    <div className="p-4 rounded-xl border border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20 max-w-md mx-auto">
                      <p className="text-sm text-amber-700 dark:text-amber-300">
                        💡 Select some datasets from the sidebar to get started
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-wrap gap-2 justify-center">
                      <Button
                        variant="outline"
                        onClick={() => setInput("Analyze the key trends and patterns in my data")}
                        className="text-sm"
                      >
                        <Brain className="h-4 w-4 mr-2" />
                        Analyze Trends
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setInput("Create a summary report of my datasets")}
                        className="text-sm"
                      >
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  {messages.map((message, index) => (
                    <MessageBubble
                      key={index}
                      message={message}
                      modelName={GEMINI_MODELS.find(m => m.id === selectedModel)?.name}
                    />
                  ))}

                  {isLoading && (
                    <div className="flex gap-3 justify-start">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                        <Sparkles className="h-4 w-4 text-white" />
                      </div>
                      <div className="max-w-[85%] rounded-2xl p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <TypingIndicator />
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </ScrollArea>

          {/* Input Area */}
          <div className="p-6 border-t border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <div className="max-w-4xl mx-auto">
              <ChatInput
                value={input}
                onChange={setInput}
                onSend={handleSendMessage}
                onImageUpload={handleImageUpload}
                uploadedImages={uploadedImages}
                onRemoveImage={removeImage}
                isLoading={isLoading}
                disabled={selectedDatasets.length === 0}
                placeholder={
                  selectedDatasets.length === 0
                    ? "Select datasets from the sidebar to start chatting..."
                    : "Ask about your data, request analysis, or upload images for OCR..."
                }
                supportsImages={GEMINI_MODELS.find(m => m.id === selectedModel)?.supportsImages}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
                      <ImageIcon className="h-4 w-4" />
                      Add Images for OCR
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            )}
          </CardHeader>
          <CardContent className="flex-1 flex flex-col p-0 min-h-0 bg-muted">
            {/* Messages */}
            <ScrollArea className="flex-1 p-2" ref={scrollAreaRef}>
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <AITextLoading />
                </div>
              ) : messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <div className="text-muted-foreground text-sm">
                    You can chat with your datasets, analyze them, and perform RAG (Retrieval-Augmented Generation).
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {messages.map((message, index) => (
                    <div
                      key={index}
                      className={cn(
                        "flex gap-2",
                        message.role === 'user' ? "justify-end" : "justify-start"
                      )}
                    >
                      <div
                        className={cn(
                          "max-w-[80%] rounded-2xl p-3 border text-base shadow-sm transition-all",
                          message.role === 'user'
                            ? "bg-primary text-primary-foreground ml-auto"
                            : "bg-background text-foreground mr-auto border-muted"
                        )}
                      >
                        <div className="flex items-center gap-1 mb-1">
                          <div className={cn(
                            "p-1 rounded-full",
                            message.role === 'user'
                              ? "bg-white/20"
                              : "bg-primary/10"
                          )}>
                            {message.role === 'user' ? (
                              <User className="h-3 w-3" />
                            ) : (
                              <Sparkles className="h-3 w-3 text-primary" />
                            )}
                          </div>
                          <span className="text-xs font-medium">
                            {message.role === 'user' ? 'You' : `AI Assistant`}
                          </span>
                          {message.model && message.role === 'assistant' && (
                            <Badge variant="outline" className="text-xs">
                              <Gem className="h-3 w-3 mr-1" />
                              {GEMINI_MODELS.find(m => m.id === message.model)?.name || message.model}
                            </Badge>
                          )}
                          <span className="text-xs ml-auto">
                            {message.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        {/* User Images */}
                        {message.images && message.images.length > 0 && (
                          <div className="mb-2">
                            <div className="flex flex-wrap gap-1">
                              {message.images.map((img, i) => (
                                <div key={i} className="relative">
                                  <img
                                    src={img.url}
                                    alt={img.name}
                                    className="w-10 h-10 object-cover rounded border"
                                  />
                                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-[10px] p-0.5 rounded-b truncate">
                                    {img.name}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        <div className="prose prose-xs max-w-none">
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {message.content}
                          </ReactMarkdown>
                        </div>
                        {/* Sources */}
                        {message.sources && message.sources.length > 0 && (
                          <div className="mt-2 pt-2 border-t">
                            <div className="flex items-center gap-1 mb-1">
                              <Quote className="h-4 w-4 text-primary" />
                              <span className="text-xs font-medium">Sources</span>
                            </div>
                            <div className="space-y-1">
                              {message.sources.map((source, i) => (
                                <div key={i} className="flex items-start gap-1 p-1 bg-muted rounded">
                                  <div className="flex-shrink-0 w-5 h-5 bg-primary/10 rounded-full flex items-center justify-center">
                                    <span className="text-[10px] font-medium text-primary">
                                      {i + 1}
                                    </span>
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-1 mb-0.5">
                                      <Badge variant="outline" className="text-[10px]">
                                        <Database className="h-3 w-3 mr-1" />
                                        {source.dataset}
                                      </Badge>
                                      <Badge variant="secondary" className="text-[10px]">
                                        {source.type}
                                      </Badge>
                                    </div>
                                    <p className="text-[10px] text-muted-foreground">
                                      {source.reference}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        {/* Datasets Analyzed */}
                        {message.datasetsAnalyzed && message.datasetsAnalyzed.length > 0 && (
                          <div className="mt-2 pt-2 border-t">
                            <div className="flex items-center gap-1 mb-1">
                              <BarChart3 className="h-4 w-4 text-primary" />
                              <span className="text-xs font-medium">
                                Analyzed Datasets
                              </span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {message.datasetsAnalyzed.map((ds, i) => (
                                <Badge key={i} variant="outline" className="text-[10px]">
                                  <Database className="h-3 w-3 mr-1" />
                                  {ds.name} ({ds.totalRows.toLocaleString()} rows)
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
            <Separator />
            {/* Input Area */}
            <div className="p-2 border-t bg-muted/50">
              {/* Uploaded Images Preview */}
              {uploadedImages.length > 0 && (
                <div className="mb-2 p-2 rounded border bg-background shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="p-1 bg-muted rounded-lg">
                      <ImageIcon className="h-4 w-4 text-primary" />
                    </div>
                    <span className="text-xs font-semibold text-foreground">Uploaded Images</span>
                    <Badge variant="secondary" className="text-xs">
                      {uploadedImages.length}
                    </Badge>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {uploadedImages.map((img, i) => (
                      <div key={i} className="relative group">
                        <img
                          src={img.url}
                          alt={img.name}
                          className="w-12 h-12 object-cover rounded border shadow"
                        />
                        <button
                          onClick={() => removeImage(i)}
                          className="absolute -top-2 -right-2 w-5 h-5 bg-destructive text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-all duration-200 shadow"
                        >
                          ×
                        </button>
                        <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-[10px] p-0.5 rounded-b truncate">
                          {img.name}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {/* ChatGPT-style input bar with drag-and-drop */}
              <form
                className={cn(
                  "flex items-end gap-2 w-full p-2 rounded-2xl bg-background shadow border transition-all",
                  isDragActive && "border-primary ring-2 ring-primary/30"
                )}
                onSubmit={e => {
                  e.preventDefault();
                  handleSendMessage();
                }}
                autoComplete="off"
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="relative flex-1">
                  <Input
                    ref={inputRef}
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={
                      uploadedImages.length > 0
                        ? "Ask about your data or uploaded images..."
                        : "Type your message..."
                    }
                    disabled={isLoading}
                    className="pr-10 h-12 rounded-full border bg-background focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none"
                  />
                  {/* Image Upload Button */}
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={!GEMINI_MODELS.find(m => m.id === selectedModel)?.supportsImages || isLoading}
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                    tabIndex={-1}
                  >
                    <ImageIcon className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </div>
                <Button
                  type="submit"
                  disabled={(!input.trim() && uploadedImages.length === 0) || isLoading}
                  className="h-12 w-12 rounded-full flex items-center justify-center shadow bg-primary text-primary-foreground hover:bg-primary/90"
                >
                  {isLoading ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <Send className="h-5 w-5" />
                  )}
                </Button>
              </form>
              {/* Model image warning remains */}
              {uploadedImages.length > 0 && !GEMINI_MODELS.find(m => m.id === selectedModel)?.supportsImages && (
                <div className="mt-2 px-2 py-1 rounded border flex items-center justify-center gap-2 text-center">
                  <span className="text-xs text-amber-700 font-medium">
                    ⚠️ Current model doesn't support images. Switch to Gemini 1.5 Pro or Flash for image analysis.
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SimpleAIChatInterface;
