'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Database, 
  BarChart3, 
  FileText,
  Layers,
  TrendingUp
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Dataset {
  id: string
  name: string
  description?: string
  data: any[]
  headers: string[]
}

interface DatasetSidebarProps {
  datasets: Dataset[]
  selectedDatasets: string[]
  onDatasetToggle: (datasetId: string) => void
}

export function DatasetSidebar({ 
  datasets, 
  selectedDatasets, 
  onDatasetToggle 
}: DatasetSidebarProps) {
  const selectedDatasetsInfo = datasets.filter(d => selectedDatasets.includes(d.id))
  const totalRows = selectedDatasetsInfo.reduce((sum, d) => sum + (d.data?.length || 0), 0)
  const totalColumns = selectedDatasetsInfo.reduce((sum, d) => sum + (d.headers?.length || 0), 0)

  return (
    <div className="w-80 flex-shrink-0 flex flex-col h-full bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950 border-r border-gray-200 dark:border-gray-800">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center gap-3 mb-3">
          <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
            <Database className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="font-semibold text-lg">Datasets</h2>
            <p className="text-sm text-muted-foreground">
              Select data sources for analysis
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        {selectedDatasets.length > 0 && (
          <div className="grid grid-cols-2 gap-2">
            <div className="p-2 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <div className="flex items-center gap-1">
                <Layers className="h-3 w-3 text-blue-600" />
                <span className="text-xs font-medium text-blue-600">Selected</span>
              </div>
              <p className="text-sm font-bold text-blue-700 dark:text-blue-400">
                {selectedDatasets.length}
              </p>
            </div>
            <div className="p-2 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <div className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3 text-green-600" />
                <span className="text-xs font-medium text-green-600">Rows</span>
              </div>
              <p className="text-sm font-bold text-green-700 dark:text-green-400">
                {totalRows.toLocaleString()}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Dataset List */}
      <div className="flex-1 flex flex-col min-h-0">
        {datasets.length === 0 ? (
          <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-2xl flex items-center justify-center mb-4">
              <Database className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              No datasets available
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Upload some data to get started with AI analysis
            </p>
          </div>
        ) : (
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-3">
              {datasets.map((dataset) => {
                const isSelected = selectedDatasets.includes(dataset.id)
                const rowCount = dataset.data?.length || 0
                const columnCount = dataset.headers?.length || 0

                return (
                  <Card
                    key={dataset.id}
                    className={cn(
                      "cursor-pointer transition-all duration-200 hover:shadow-md border-2",
                      isSelected
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20 shadow-md"
                        : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                    )}
                    onClick={() => onDatasetToggle(dataset.id)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={isSelected}
                          onChange={() => {}} // Handled by card click
                          className="mt-1"
                        />
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-sm font-medium truncate">
                            {dataset.name}
                          </CardTitle>
                          {dataset.description && (
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {dataset.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3 text-gray-500" />
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                              {rowCount.toLocaleString()} rows
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3 text-gray-500" />
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                              {columnCount} cols
                            </span>
                          </div>
                        </div>
                        
                        {isSelected && (
                          <Badge variant="default" className="text-xs bg-blue-500">
                            Selected
                          </Badge>
                        )}
                      </div>

                      {/* Column Preview */}
                      {dataset.headers && dataset.headers.length > 0 && (
                        <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-xs text-gray-500 mb-1">Columns:</p>
                          <div className="flex flex-wrap gap-1">
                            {dataset.headers.slice(0, 3).map((header, i) => (
                              <Badge key={i} variant="outline" className="text-xs">
                                {header}
                              </Badge>
                            ))}
                            {dataset.headers.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{dataset.headers.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Summary Footer */}
      {selectedDatasets.length > 0 && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Analysis Ready</span>
              <Badge variant="secondary" className="bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300">
                {selectedDatasets.length} dataset{selectedDatasets.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
              <div>
                <span className="font-medium">{totalRows.toLocaleString()}</span> total rows
              </div>
              <div>
                <span className="font-medium">{totalColumns}</span> total columns
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
